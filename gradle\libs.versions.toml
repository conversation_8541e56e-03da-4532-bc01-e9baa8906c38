[versions]
kotlin = "2.2.20"
adventure = "4.25.0"
bstats = "3.1.0"
exposed = "1.0.0-rc-1"
h2database = "2.2.224"
hikariCP = "7.0.1"
kotlinx-io-core = "0.8.0"
mariadb-client = "3.5.6"
nbt-api = "2.15.3"
packetevents = "2.10.0"

[libraries]
adventure-api = { group = "net.kyori", name = "adventure-api", version.ref = "adventure" }
adventure-text-minimessage = { group = "net.kyori", name = "adventure-text-minimessage", version.ref = "adventure" }
adventure-text-serializer-ansi = { group = "net.kyori", name = "adventure-text-serializer-ansi", version.ref = "adventure" }
adventure-text-serializer-gson = { group = "net.kyori", name = "adventure-text-serializer-gson", version.ref = "adventure" }
adventure-text-serializer-legacy = { group = "net.kyori", name = "adventure-text-serializer-legacy", version.ref = "adventure" }
adventure-text-serializer-plain = { group = "net.kyori", name = "adventure-text-serializer-plain", version.ref = "adventure" }
bstats-bukkit = { group = "org.bstats", name = "bstats-bukkit", version.ref = "bstats" }
bstats-velocity = { group = "org.bstats", name = "bstats-velocity", version.ref = "bstats" }
exposed-core = { group = "org.jetbrains.exposed", name = "exposed-core", version.ref = "exposed" }
exposed-dao = { group = "org.jetbrains.exposed", name = "exposed-dao", version.ref = "exposed" }
exposed-jdbc = { group = "org.jetbrains.exposed", name = "exposed-jdbc", version.ref = "exposed" }
exposed-kotlin-datetime = { group = "org.jetbrains.exposed", name = "exposed-kotlin-datetime", version.ref = "exposed" }
exposed-json = { group = "org.jetbrains.exposed", name = "exposed-json", version.ref = "exposed" }
hikariCP = { group = "com.zaxxer", name = "HikariCP", version.ref = "hikariCP" }
kotlinx-io-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-io-core", version.ref = "kotlinx-io-core" }
nbt-api = { group = "de.tr7zw", name = "item-nbt-api", version.ref = "nbt-api" }
packetevents-api = { group = "com.github.retrooper", name = "packetevents-api", version.ref = "packetevents" }
packetevents-spigot = { group = "com.github.retrooper", name = "packetevents-spigot", version.ref = "packetevents" }
packetevents-velocity = { group = "com.github.retrooper", name = "packetevents-velocity", version.ref = "packetevents" }