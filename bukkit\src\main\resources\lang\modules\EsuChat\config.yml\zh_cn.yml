chat:
  # 'player' 是游戏内玩家看到的消息；'console' 是控制台看到的消息。
  format:
  # 启用此功能后，玩家只能收到同世界中附近玩家的公屏聊天消息。
  ranged-chat:
    # 拥有 `esu.esuchat.chat.shout` 权限的玩家可以使用这个前缀发出全服聊天。
    # 玩家默认拥有此权限。
    # 需要在 prefixed-message-modifiers 的前缀前使用此前缀。
    shout-prefix:
  # 若玩家发送的消息以 'message-prefix' 开头,
  # 则聊天消息会被更改为 'format'。
  # 可以修改消息格式。
  prefixed-message-modifiers:
emote:
  # 启用 ESU emote/me 指令.
  enabled:
  # 启用后，所有 emote 指令将被重定向到 ESU，
  # 防止与其他聊天指令的混用。
  intercept-namespaces:
whisper:
  # 启用 ESU 私聊指令.
  enabled:
