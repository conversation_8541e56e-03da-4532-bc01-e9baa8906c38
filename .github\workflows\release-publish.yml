name: ESU Release Publish

on:
  release:
    types:
      - published
      - edited

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 250

      - name: Set up JDK 22
        uses: actions/setup-java@v4
        with:
          java-version: '22'
          distribution: 'temurin'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          gradle-version: '9.2.1'

      - name: Modify Modrinth changelog
        if: ${{ github.event_name == 'release' }}
        env:
          MODRINTH_TOKEN: ${{ secrets.MODRINTH_TOKEN }}
          CHANGELOG_CONTENT: ${{ github.event.release.body }}
          GIT_TAG: ${{ github.event.release.tag_name }}
        run: gradle editChangelog --stacktrace