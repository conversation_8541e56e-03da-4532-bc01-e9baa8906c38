name: ESU
main: io.github.rothes.esu.bukkit.EsuBootstrapBukkit
version: @finalVersionName@
api-version: 1.13
author: <PERSON><PERSON>
folia-supported: true
softdepend:
  - AuthMe
  - CraftEngine
  - packetevents
  - PlaceholderAPI
  - PlugMan
  - PlugManX

permissions:
  'esu.essentialCommands.player':
    default: true
    children:
      'esu.essentialCommands.ping.command': true
      'esu.essentialCommands.ping.command.others': true
      'esu.essentialCommands.suicide.command': true

  'esu.esuChat.chat.shout':
    default: true


  'esu.antiCommandSpam.admin':
    default: op
    children:
      'esu.antiCommandSpam.exempt': true

  'esu.chatAntiSpam.admin':
    default: op
    children:
      'esu.chatAntiSpam.bypass': true
      'esu.chatAntiSpam.notify': true

  'esu.essentialCommands.admin':
    default: op
    children:
      'esu.essentialCommands.clientLocale.command': true
      'esu.essentialCommands.dimensionTravel.command': true
      'esu.essentialCommands.dimensionTravel.command.others': true
      'esu.essentialCommands.heal.command': true
      'esu.essentialCommands.heal.command.others': true
      'esu.essentialCommands.feed.command': true
      'esu.essentialCommands.feed.command.others': true
      'esu.essentialCommands.ip.command': true
      'esu.essentialCommands.ipGroup.command': true
      'esu.essentialCommands.playerChunkTickets.command.genRateTop': true
      'esu.essentialCommands.playerChunkTickets.command.loadRateTop': true
      'esu.essentialCommands.playerChunkTickets.command.tickDistance': true
      'esu.essentialCommands.playerChunkTickets.command.viewDistance': true
      'esu.essentialCommands.playerChunkTickets.command.sendDistance': true
      'esu.essentialCommands.speed.command': true
      'esu.essentialCommands.speed.command.others': true
      'esu.essentialCommands.tpChunk.command': true
      'esu.essentialCommands.tpChunk.command.others': true

  'esu.esuChat.admin':
    default: op
    children:
      'esu.esuChat.spy': true
      'esu.esuChat.spy.other': true
      'esu.esuChat.spy.enableOnJoin': true

  'esu.networkThrottle.admin':
    default: op
    children:
      'esu.networkThrottle.command.analyser': true
      'esu.networkThrottle.command.chunkDataThrottle': true

  'esu.news.admin':
    default: op
    children:
      'esu.news.command.editor': true