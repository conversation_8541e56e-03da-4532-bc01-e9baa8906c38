package io.github.rothes.esu.bukkit.event

import fr.xephi.authme.api.v3.AuthMeApi
import fr.xephi.authme.events.LoginEvent
import io.github.rothes.esu.bukkit.bootstrap
import io.github.rothes.esu.bukkit.user
import org.bukkit.Bukkit
import org.bukkit.entity.Player
import org.bukkit.event.EventHandler
import org.bukkit.event.HandlerList
import org.bukkit.event.Listener
import org.bukkit.event.player.PlayerJoinEvent

/**
 * Called when a player user authenticates and logins their account.
 */
class UserLoginEvent(
    player: Player
): EsuUserEvent(player) {

    override fun getHandlers(): HandlerList = Companion.handlers

    companion object {
        private val handlers = HandlerList()
        @JvmStatic
        fun getHandlerList(): HandlerList = handlers

        init {
            fun reg(listener: Listener, filter: (Player) -> Boolean) {
                Bukkit.getPluginManager().registerEvents(listener, bootstrap)
                Bukkit.getOnlinePlayers().filter(filter).forEach { it.user.logonBefore = true }
            }
            if (Bukkit.getPluginManager().isPluginEnabled("AuthMe")) {
                reg(object : Listener {
                    @EventHandler
                    fun onLogin(e: LoginEvent) {
                        Bukkit.getPluginManager().callEvent(UserLoginEvent(e.player))
                    }
                }) { AuthMeApi.getInstance().isAuthenticated(it) }
            } else {
                reg(object : Listener {
                    @EventHandler
                    fun onLogin(e: PlayerJoinEvent) {
                        Bukkit.getPluginManager().callEvent(UserLoginEvent(e.player))
                    }
                }) { true }
            }
            // Internal event
            Bukkit.getPluginManager().registerEvents(object : Listener {
                @EventHandler
                fun onLogin(e: UserLoginEvent) {
                    e.user.logonBefore = true
                }
            }, bootstrap)
        }
    }
}