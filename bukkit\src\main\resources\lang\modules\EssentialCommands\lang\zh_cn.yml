unsafe-teleport-spot: <ec>未能找到一个安全的传送地点.
teleporting-player: <tc>正在传送 <tdc><player></tdc>...
client-locale:
  message: <pdc><player><pc> 的客户端语言为 <sdc><locale>
dimension-travel:
  unsupported-the-end: <ec>不能在末地进行维度切换.
heal:
  healed: <pc>你已被治愈.
  healed-player: <pc>已治愈 <pdc><player></pc>.
invulnerable:
  enabled-for-player: <pc>已为 <pdc><player><pc> 开启无敌模式.
  disabled-for-player: <pc>已为 <pdc><player><pc> 关闭无敌模式.
  enabled: <pc>你将不再受到任何伤害.
  disabled: <pc>现在开始你将会受到伤害.
feed:
  fed: <pc>你已被喂饱.
  fed-player: <pc>已喂饱 <pdc><player></pc>.
ip:
  message: <pdc><player><pc> 的IP地址为 <sdc><address>
ip-group:
  no-same-ip: <pc>没有具有相同IP地址的玩家.
no-fall:
  enabled-for-player: <pc>已为 <pdc><player><pc> 开启无摔落伤害.
  disabled-for-player: <pc>已为 <pdc><player><pc> 关闭无摔落伤害.
  enabled: <pc>你将不再受到摔落伤害.
  disabled: <pc>现在开始你将会受到摔落伤害.
ping:
  message: <pdc><player><pc> 的网络延迟为 <sdc><ping><sc>ms
player-chunk-tickets:
  gen-rate-top:
    no-data: <pc>目前没有区块生成请求.
    header: '<pdc>[玩家]<pc>: <sc>[区块生成请求/秒]'
  load-rate-top:
    no-data: <pc>目前没有区块加载请求.
    header: '<pdc>[玩家]<pc>: <sc>[区块加载请求/秒]'
spectate:
  cannot-spectate-self: <ec>无法旁观自己!
  spectating-target: <pc>正在旁观 <pdc><target></pdc>.
speed:
  speed-get: <pdc><player><pc> 的<type>速度为 <pdc><value><pc>.
  speed-set: <pc>已设置 <pdc><player><pc> 的<type>速度为 <pdc><value><pc>.
  walk-speed:
    type-name: 行走
  fly-speed:
    type-name: 飞行
suicide:
  placeholders:
    suicided: (自杀了)
  confirm-button: <edc><hover:show_text:'<ec>我不活了...'>[确认]
  confirm-suicide: <ec>你确定要自杀吗? 背包内的所有物品都将掉落! <confirm>
