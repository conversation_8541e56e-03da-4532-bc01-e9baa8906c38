package io.github.rothes.esu.core.user

import io.github.rothes.esu.core.config.EsuConfig
import io.github.rothes.esu.core.configuration.ConfigurationPart
import io.github.rothes.esu.core.configuration.MultiLangConfiguration
import io.github.rothes.esu.core.configuration.data.MessageData
import io.github.rothes.esu.lib.adventure.text.Component
import io.github.rothes.esu.lib.adventure.text.TranslatableComponent
import io.github.rothes.esu.lib.adventure.text.flattener.ComponentFlattener
import io.github.rothes.esu.lib.adventure.text.minimessage.tag.resolver.TagResolver
import io.github.rothes.esu.lib.adventure.text.serializer.ansi.ANSIComponentSerializer
import io.github.rothes.esu.lib.adventure.translation.GlobalTranslator
import io.github.rothes.esu.lib.adventure.translation.TranslationRegistry
import io.github.rothes.esu.lib.net.kyori.ansi.ColorLevel
import java.util.*

interface LogUser: User {

    @Suppress("INAPPLICABLE_JVM_NAME")
    @JvmName("logMessage")
    fun <T: ConfigurationPart> log(locales: MultiLangConfiguration<T>, block: T.() -> MessageData?, vararg params: TagResolver) {
        val messageData = localed(locales, block)
        log(messageData, params = params)
    }

    fun <T: ConfigurationPart> log(locales: MultiLangConfiguration<T>, block: T.() -> String?, vararg params: TagResolver) {
        val message = localed(locales, block)
        miniMessage("[ESU] $message", params = params)
    }

    fun log(message: String, vararg params: TagResolver) {
        if (message.isEmpty())
            miniMessage("[ESU] $message", params = params)
    }

    fun log(message: MessageData, vararg params: TagResolver) {
        if (message.chat.isNullOrEmpty())
            message(message, params = params)
        else
            message(message.copy(chat = message.chat.map { "[ESU] $it" }), params = params)
    }

    override fun actionBar(message: Component) {
        // Relocate actionBar messages to chat/console message
        message(message)
    }

    override fun message(message: Component) {
        if (EsuConfig.get().forceTrueColorConsole)
            print(serializer.serialize(message))
        else
            super.message(message)
    }

    fun print(string: String)

    companion object {
        private var flattener = ComponentFlattener.basic().toBuilder().complexMapper(TranslatableComponent::class.java) { translatable, consumer ->
            for (source in GlobalTranslator.translator().sources()) {
                if (source is TranslationRegistry && source.contains(translatable.key())) {
                    consumer.accept(GlobalTranslator.render(translatable, Locale.getDefault()))
                    return@complexMapper
                }
            }
            val fallback = translatable.fallback() ?: return@complexMapper
            for (source in GlobalTranslator.translator().sources()) {
                if (source is TranslationRegistry && source.contains(fallback)) {
                    consumer.accept(GlobalTranslator.render(Component.translatable(fallback), Locale.getDefault()))
                    return@complexMapper
                }
            }
        }.build()
        private var serializer = buildSerializer()

        fun setFlattener(flattener: ComponentFlattener) {
            this.flattener = flattener
            serializer = buildSerializer()
        }

        private fun buildSerializer() = ANSIComponentSerializer.builder()
            .colorLevel(ColorLevel.TRUE_COLOR)
            .flattener(flattener)
            .build()
    }

}