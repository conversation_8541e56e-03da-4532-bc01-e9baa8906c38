rootProject.name = "ESU"

include("module")
include("module:off-heap-memory")

include("core")
include("core:dep-core")
include("core:dep-impl-core")
include("common")

include("bukkit")
include("bukkit:dep-bukkit")
include("bukkit:module")
include("bukkit:module:bukkit-versions")
include("bukkit:version")
include("bukkit:version:base")
include("bukkit:version:v1_17_1")
include("bukkit:version:v1_18")
include("bukkit:version:v1_18_2")
include("bukkit:version:v1_19_3")
include("bukkit:version:v1_20")
include("bukkit:version:v1_20_2")
include("bukkit:version:v1_20_4")
include("bukkit:version:v1_21")
include("bukkit:version:v1_21_1")
include("bukkit:version:v1_21_3")
include("bukkit:version:v1_21_6")
include("bukkit:version:v1_21_11")

include("velocity")
include("velocity:dep-velocity")