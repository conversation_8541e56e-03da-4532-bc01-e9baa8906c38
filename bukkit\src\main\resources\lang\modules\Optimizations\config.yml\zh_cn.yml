# 更改服务器标签类型设置。
# 标签用于控制区块的加载。详情请参阅 https://zh.minecraft.wiki/w/区块#标签
# 将 expiry-ticks 设置为 0 或负值将使得区块永久加载，直到标签被手动移除。
ticket-type:
  # 每次启用或重新加载模块时，会将标签的持续游戏刻(expiry-ticks)修改为以下值。
  # 例如，如果设置“portal: 1”，则通过传送门传送加载的区块将在 1 个游戏刻后立即卸载，
  # 由此可导致“区块加载器”将不再有用。
  startup-settings:
waterlogged:
  # 若启用此选项，将禁止含水方块中的水流动。
  disable-water-spread:

  # 若启用此选项，每次活塞推动后，含水方块中的水都会被重新添加。
  keep-water-after-piston-push:

  # 若启用此选项，将阻止活塞推动含水方块。
  # 可阻止“水墙机”、“铺水机”的运作。
  disable-waterlogged-block-push:
