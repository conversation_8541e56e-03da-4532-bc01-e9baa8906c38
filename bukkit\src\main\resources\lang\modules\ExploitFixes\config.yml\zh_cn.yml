consumer-item:
  # 在玩家右键使用物品时，阻止在背包中移动物品。
  # 用于修复三叉戟/弓箭复制漏洞。
  # 如果运行的是 Minecraft 1.21.5 或更高版本，复制漏洞已修复，不必启用该功能。
  enabled:

  # 更详细的检测，只阻止三叉戟和弓箭复制。
  # 如果您想允许更多不正常的作弊功能，可以启用此选项。
  only-block-consumer-items:
vault-unlocking:
  # 每个宝库只会存储最近 128 名解锁过它的玩家。
  # 这意味着，如果一个玩家拥有超过 128 个账号，那么他就可以一直解锁这个宝库。
  # 为解决此漏洞，我们可以添加一个限制，宝库在一段时间内只能开启一定次数，
  # 从而防止玩家轻易地利用此漏洞。
  enabled:

  # 宝库解锁记录的过期时间。
  unlock-expiry:

  # 每个宝库允许的解锁记录最大值。
  # 一旦超过，这个宝库将不能解锁，直到有一个解锁记录过期。
  max-unlocks-per-vault:
