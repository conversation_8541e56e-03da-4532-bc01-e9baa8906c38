name: ESU Dev Build

on:
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 250
          fetch-tags: true

      - name: Set up JDK 22
        uses: actions/setup-java@v4
        with:
          java-version: '22'
          distribution: 'temurin'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          gradle-version: '9.2.1'

      - name: Publish to Modrinth
        if: ${{ github.event_name == 'push' }}
        env:
          MODRINTH_TOKEN: ${{ secrets.MODRINTH_TOKEN }}
        run: gradle modrinth --stacktrace