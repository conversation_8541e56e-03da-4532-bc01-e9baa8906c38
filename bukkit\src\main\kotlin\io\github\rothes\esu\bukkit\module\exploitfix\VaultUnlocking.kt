package io.github.rothes.esu.bukkit.module.exploitfix

import io.github.rothes.esu.bukkit.module.ExploitFixesModule
import io.github.rothes.esu.bukkit.plugin
import io.github.rothes.esu.bukkit.user
import io.github.rothes.esu.bukkit.util.CoordinateUtils.blockKey_
import io.github.rothes.esu.bukkit.util.ServerCompatibility
import io.github.rothes.esu.bukkit.util.extension.ListenerExt.register
import io.github.rothes.esu.bukkit.util.extension.ListenerExt.unregister
import io.github.rothes.esu.core.coroutine.AsyncScope
import io.github.rothes.esu.core.util.CollectionUtils.removeWhile
import io.github.rothes.esu.core.util.ComponentUtils.duration
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap
import it.unimi.dsi.fastutil.longs.LongArrayList
import it.unimi.dsi.fastutil.longs.LongList
import it.unimi.dsi.fastutil.objects.Object2ObjectOpenHashMap
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.bukkit.Material
import org.bukkit.World
import org.bukkit.block.Block
import org.bukkit.block.Vault
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.block.Action
import org.bukkit.event.player.PlayerInteractEvent
import kotlin.concurrent.atomics.AtomicBoolean
import kotlin.concurrent.atomics.ExperimentalAtomicApi
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.minutes

@OptIn(ExperimentalAtomicApi::class)
internal object VaultUnlocking: Listener {

    private var enabled = AtomicBoolean(false)
    private val opened = Object2ObjectOpenHashMap<World, Long2ObjectOpenHashMap<LongList>>()
    private var purgeTask: Job? = null

    private fun getBlockTimes(block: Block) =
        opened.getOrPut(block.world) { Long2ObjectOpenHashMap() }.getOrPut(block.blockKey_) { LongArrayList() }

    fun onEnable() {
        if (ExploitFixesModule.config.vaultUnlocking.enabled) {
            if (ServerCompatibility.serverVersion < 21) {
                plugin.err("[VaultUnlocking] This feature requires Bukkit 1.21+")
                return
            }
            if (enabled.compareAndSet(expectedValue = false, newValue = true)) {
                this.register()
                purgeTask = AsyncScope.launch {
                    while (isActive) {
                        delay(30.minutes)
                        val now = System.currentTimeMillis()
                        val config = ExploitFixesModule.config.vaultUnlocking
                        val expired = now - config.unlockExpiry.toMillis()
                        val iterator = opened.iterator()
                        for ((_, map) in iterator) {
                            val iterator2 = map.iterator()
                            for ((_, list) in iterator2) {
                                list.removeWhile { it < expired }
                                if (list.isEmpty()) {
                                    iterator2.remove()
                                }
                            }
                            if (map.isEmpty()) {
                                iterator.remove()
                            }
                        }
                    }
                }
            }
        }
    }

    fun onReload() {
        if (ExploitFixesModule.enabled && ExploitFixesModule.config.vaultUnlocking.enabled) {
            onEnable()
        } else {
            onDisable()
        }
    }

    fun onDisable() {
        if (enabled.compareAndSet(expectedValue = true, newValue = false)) {
            this.unregister()
            opened.clear()
            purgeTask?.cancel().also { purgeTask = null }
        }
    }


    @EventHandler
    fun onInteract(event: PlayerInteractEvent) {
        if (event.action != Action.RIGHT_CLICK_BLOCK) return
        if (event.player.isSneaking) return

        val block = event.clickedBlock ?: return
        if (block.type != Material.VAULT) return
        val state = block.state as Vault

        if (!state.keyItem.isSimilar(event.item) || !state.hasConnectedPlayer(event.player.uniqueId))
            return

        val now = System.currentTimeMillis()
        val config = ExploitFixesModule.config.vaultUnlocking
        val expired = now - config.unlockExpiry.toMillis()
        val times = getBlockTimes(block)
        times.removeWhile { it < expired }
        if (times.size < config.maxUnlocksPerVault) {
            times.add(now)
        } else {
            event.isCancelled = true
            val duration = if (times.isNotEmpty()) (times.getLong(0) - expired).milliseconds else Duration.INFINITE
            val user = event.player.user
            user.message(ExploitFixesModule.lang, { vaultUnlocking.tooManyUnlocks }, duration(duration, user))
        }
    }


}