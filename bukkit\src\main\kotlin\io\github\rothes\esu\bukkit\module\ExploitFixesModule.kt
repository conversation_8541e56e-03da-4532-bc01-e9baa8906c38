package io.github.rothes.esu.bukkit.module

import io.github.rothes.esu.bukkit.module.exploitfix.VaultUnlocking
import io.github.rothes.esu.bukkit.util.extension.ListenerExt.register
import io.github.rothes.esu.bukkit.util.extension.ListenerExt.unregister
import io.github.rothes.esu.core.configuration.ConfigurationPart
import io.github.rothes.esu.core.configuration.data.MessageData
import io.github.rothes.esu.core.configuration.data.MessageData.Companion.message
import io.github.rothes.esu.core.configuration.meta.Comment
import io.github.rothes.esu.core.module.configuration.BaseModuleConfiguration
import org.bukkit.Material
import org.bukkit.event.EventHandler
import org.bukkit.event.Listener
import org.bukkit.event.inventory.InventoryClickEvent
import java.time.Duration

object ExploitFixesModule: BukkitModule<ExploitFixesModule.ModuleConfig, ExploitFixesModule.ModuleLang>() {

    override fun onEnable() {
        Listeners.register()
        VaultUnlocking.onEnable()
    }

    override fun onDisable() {
        super.onDisable()
        Listeners.unregister()
        VaultUnlocking.onDisable()
    }

    override fun onReload() {
        super.onReload()
        VaultUnlocking.onReload()
    }

    private object Listeners: Listener {
        @EventHandler
        fun onInvClick(e: InventoryClickEvent) {
            val config = config
            if (!config.consumerItem.enabled)
                return
            val player = e.viewers.firstOrNull() ?: return
            if (player.isHandRaised) {
                if (config.consumerItem.onlyBlockConsumerItems) {
                    if (e.hotbarButton == -1) return

                    val item = player.inventory.getItem(e.hotbarButton) ?: return
                    val type = item.type
                    if (type != Material.BOW && type != Material.TRIDENT)
                        return
                }
                e.isCancelled = true
            }
        }
    }

    data class ModuleConfig(
        val consumerItem: ConsumerItem = ConsumerItem(),
        val vaultUnlocking: VaultUnlocking = VaultUnlocking(),
    ): BaseModuleConfiguration() {

        data class ConsumerItem(
            @Comment("""
                Blocks moving or clicking items in inventory while player is using item.
                This is for fixing trident/bow duplication exploit.
                If you are running Minecraft 1.21.5 or above, duplication is already fixed, and you don't need this.
            """)
            val enabled: Boolean = false,
            @Comment("""
                More detailed detect to block trident/bow duplication only.
                If you want to allow more cheats, enable this.
            """)
            val onlyBlockConsumerItems: Boolean = false,
        )

        data class VaultUnlocking(
            @Comment("""
                Each vault keeps track of up to 128 latest unique players that have unlocked it. Which means, if a player
                 owns more than 128 accounts, they can unlock the vault sequentially and indefinitely.
                To solve this, we added a restriction that a vault can only be opened a specified number of times
                 within a period of time, so they cannot easily abuse it.
            """)
            val enabled: Boolean = false,
            @Comment("Each vault unlock expiry time")
            val unlockExpiry: Duration = Duration.ofMinutes(30),
            @Comment("""
                Max allowed recorded unlocks per vault.
                Once exceeded, this vault cannot be unlocked until one record expires.
            """)
            val maxUnlocksPerVault: Int = 10,
        )
    }

    data class ModuleLang(
        val vaultUnlocking: VaultUnlocking = VaultUnlocking(),
    ): ConfigurationPart {

        data class VaultUnlocking(
            val tooManyUnlocks: MessageData = "<actionbar><ec>Please wait <duration> before unlocking this vault".message,
        )
    }

}