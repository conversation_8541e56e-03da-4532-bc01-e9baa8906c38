# 有助于降低区块上传带宽。插件会压缩区块数据包中的不可见的方块数据。
# 在必要时，再次发送完整的区块数据。
# 平均可以节省约 50% 的主世界带宽使用量和 30% 的下界带宽使用量。
# 确保您已在群组服务器或本服务器上启用网络压缩(network-compression)。
chunk-data-throttle:
  chunk-handler:
    # 如果重新发送的方块数量超过此值，将直接重新发送整个区块的完整原始数据。
    # 设置为 -1 表示永远不会重新发送整个区块，而是持续更新相邻方块；
    # 设置为 0 表示始终重新发送完整的原始区块。
    # 设置为较大的值可以避免持续发送方块更新数据包，但尚未确定是否有益。
    # 此外，原始区块不会包含假矿功能。建议保留为 -1 。
    threshold-to-resent-whole-chunk:
    # 当玩家开始挖掘一个方块时，我们会立即更新相邻的方块。
    # 如果启用此选项，我们将通过粗略计算来检查该方块是否在玩家的合法交互范围内。
    update-on-legal-interact-only:
    # 每次更新方块时，更新多少距离内的相邻方块。
    update-distance:
    # 除非身处虚空，否则基岩层（世界最小高度）永远不可见。
    # 默认我们会跳过这项检查，如果你不喜欢，可以启用。
    minimal-height-invisible-check:
    # 与 minimal-height 相似，适用于下界的基岩顶层. 为了开箱即用，默认为 true。
    # 如果您不允许玩家上基岩层，强烈建议将此选项设为 FALSE。
    nether-roof-invisible-check:
    # 如果一个透明方块被非透明方块包围，则明显中心方块不可见。
    # 此外，是否应该认为所有相邻的方块对于该方块面是不可见的？
    # 除非玩家加入游戏时视线在这个中心方块内，否则永远不会看到相邻方块。
    # 此步骤额外耗时约 0.02 毫秒，因此默认情况下不启用。
    # 启用此选项有助于节省下界的带宽，因为下界中有很多单方块的熔岩。
    detect-invisible-single-block:
    # 检测熔岩池，将被覆盖的熔岩方块视为不可见。
    # 此步骤额外耗时约 0.03 毫秒，因此默认情况下不启用。
    # 这还会要求插件在玩家每次移动时检测附近的方块。
    # 启用此功能有助于节省带宽，尤其是在下界。
    detect-lava-pool:
    # 此功能不支持与其他任何假矿插件同时运行。
    # 您必须使用我们提供的假矿功能。
    #
    # 我们会将不可见的方块替换为此列表中的随机一个方块。
    # 如果您不想启用假矿，可将列表设置为 'bedrock' 。
    anti-xray-random-block-list:
    # 若启用，在不增加子区块“调色板”位数时，可添加一个随机方块类型。
    # 可大幅增强假矿效果，同时仅占用额外少量字节的带宽。
    enhanced-anti-xray:
    # 在此列表中添加的任何方块在处理时会被忽略。
    # 可以添加任何不想被隐藏的方块。
    # 例如，你可以添加任何矿石，这样就不会产生反矿透效果。
    # *警告*：添加任何方块都可能严重降低带宽压缩率，请三思。
    non-invisible-blocks-overrides:

# 启用动态区块发送速率。需要在 ESU 的 velocity 插件端也开启此功能。
dynamic-chunk-send-rate:

# 智能遮挡剔除，可降低上行带宽占用。
# 插件会对玩家隐藏他们不可见的实体。
entity-culling:
  raytrace-handler:
    # 用于计算实体可见性的异步线程数量。越多则越快。
    raytrace-threads:
    # 每秒的更新次数上限。
    # 数字越大，即时性越高，但也会造成更多的性能占用。
    updates-per-second:
    # 启用 fast-raytrace 则使用固定步长判断可见性，计算速度快一倍，
    # 但透过拐角的实体可能不会隐藏，同时亦可防止经过拐角后实体突然出现。
    # 设为 false 则使用 3D-DDA 算法，适合需要判定更精确的场景。
    fast-raytrace:
    # 在第一次看见实体时标记实体为被遮挡状态。
    # 防止实体生成时的可见性闪烁和相关数据包。
    entity-culled-by-default:
    # 列表中的实体类型总是可见。
    visible-entity-types:
    # 在该半径范围内的实体总是可见。
    force-visible-distance:
    # 模拟并预测玩家在后续游戏刻的位置。
    # 实体在玩家当前位置和预测位置都不可见时，
    # 才会被判定为被遮挡状态。
    # 可以降低实体突然出现的可能性，
    # 根据玩家速度可能造成额外一倍的处理时间。
    # 需要 Minecraft 1.21+ 以获取客户端移动向量。
    predicate-player-positon:
    # 玩家的可见实体必须达到这个数量，才为他们剔除遮挡实体。
    # 设为 -1 则始终遮挡剔除。
    cull-threshold: -1

entity-update-interval:
  # 控制实体类型位置更新数据包发送的游戏刻间隔。
  # 更高的数字意味着更高的实体移动数据包，亦是更高的不同步，
  # 以换取更少的网络占用。
  entity-type-update-interval:

# 将延迟较高的玩家的视距设置调整为较低的值。
# 由此防止到影响所有玩家的平均网络质量。
high-latency-adjust:
  # 当玩家的延迟高于或等于此值时触发一次调整。
  latency-threshold:
  # 高延迟必须持续这段时间，最终才触发调整。
  duration:
  # 插件会检测 CLIENT_SETTINGS 数据包来重置玩家的视野距离。
  # 若为 true，则玩家必须更改客户端视野距离才能重置视距；
  # 若为 false，则任何新设置都可以重置玩家的视距。
  new-view-distance-to-reset:

# 启用该功能可滤过不必要的数据包，这些数据包不会为客户端带来任何改变。
# 目前仅过滤实际上实体未移动的实体位置数据包。
skip-unnecessary-packets:

_oc_:
  chunk-data-throttle:
    threshold-to-resent-whole-chunk:
      - |-
        如果重新发送的方块数量超过此值，插件将直接重新发送整个区块的完整数据。
        设置为 -1 表示永远不会重新发送整个区块，而是持续更新相邻方块；
        设置为 0 表示始终重新发送完整区块。