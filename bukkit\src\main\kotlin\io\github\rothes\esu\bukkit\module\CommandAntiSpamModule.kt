package io.github.rothes.esu.bukkit.module

import com.google.common.collect.HashBasedTable
import io.github.rothes.esu.bukkit.user
import io.github.rothes.esu.bukkit.util.extension.ListenerExt.register
import io.github.rothes.esu.bukkit.util.extension.ListenerExt.unregister
import io.github.rothes.esu.core.configuration.ConfigurationPart
import io.github.rothes.esu.core.configuration.meta.Comment
import io.github.rothes.esu.core.coroutine.AsyncScope
import io.github.rothes.esu.core.module.configuration.BaseModuleConfiguration
import io.github.rothes.esu.core.user.User
import io.github.rothes.esu.core.util.extension.DurationExt.compareTo
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.bukkit.event.EventHandler
import org.bukkit.event.EventPriority
import org.bukkit.event.Listener
import org.bukkit.event.player.PlayerCommandPreprocessEvent
import java.time.Duration
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

object CommandAntiSpamModule: BukkitModule<CommandAntiSpamModule.ModuleConfig, CommandAntiSpamModule.ModuleLocale>() {
    
    private var cacheTask: Job? = null

    override fun onEnable() {
        Listeners.register()
        cacheTask = AsyncScope.launch {
            while (isActive) {
                delay(5.minutes)
                val now = System.currentTimeMillis()
                Listeners.hits.cellSet().toList().forEach { cell ->
                    val cmd = cell.columnKey
                    val queue = cell.value
                    val conf = config.commands.find { it.commands.any { regex -> regex.containsMatchIn(cmd) } }
                    if (conf == null) {
                        queue.clear()
                    } else {
                        queue.removeIf { now - it > conf.expireInterval }
                    }
                    if (queue.isEmpty()) {
                        Listeners.hits.remove(cell.rowKey, cell.columnKey)
                    }
                }
            }
        }
    }

    override fun onDisable() {
        super.onDisable()
        Listeners.unregister()
        cacheTask?.cancel()
        cacheTask = null
    }

    object Listeners: Listener {
        
        val hits: HashBasedTable<User, String, ArrayDeque<Long>> = HashBasedTable.create()

        @EventHandler(priority = EventPriority.LOW)
        fun onCommand(event: PlayerCommandPreprocessEvent) {
            val user = event.player.user
            if (user.hasPerm("exempt")) {
                return
            }
            val command = event.message.substring(1)
            val matched = config.commands.find { group ->
                group.commands.any { cmd ->
                    cmd.containsMatchIn(command)
                }
            }
            if (matched != null) {
                val now = System.currentTimeMillis()
                val queue = hits.get(user, command) ?: ArrayDeque<Long>().also {
                    hits.put(user, command, it)
                }
                queue.removeIf { now - it > matched.expireInterval }
                queue.add(now)

                val count = queue.size
                if (matched.cancelCount in 0..count) {
                    event.isCancelled = true
                }

                if (matched.kickCount in 0..count) {
                    user.kick(lang, { kickMessage[matched.kickMessage] })
                } else if (matched.warnCount >= 0 && count >= matched.cancelCount) {
                    user.message(lang, { warnMessage[matched.warnMessage] })
                }
            }
        }
    }


    data class ModuleConfig(
        @Comment("""
            Plugin will increase the count for the command player send if it matches any condition,
             and handle the limit with the first limit it hits.
        """)
        val commands: List<CommandGroup> = arrayListOf(
            CommandGroup(listOf("^(.+:)?suicide$".toRegex(), "^(.+:)?kill$".toRegex()), "suicide-spam", "suicide-spam"),
            CommandGroup(listOf(".".toRegex()), "generic-spam", "generic-spam"),
        ),
    ): BaseModuleConfiguration() {
        
        data class CommandGroup(
            val commands: List<Regex> = arrayListOf(),
            @Comment("The message key to send to users. You need to set the message in locale configs.")
            val warnMessage: String = "",
            val kickMessage: String = "",
            val cancelCount: Int = 3,
            val warnCount: Int = 3,
            val kickCount: Int = 5,
            val expireInterval: Duration = 20.seconds.toJavaDuration(),
        ): ConfigurationPart
    }


    data class ModuleLocale(
        val warnMessage: Map<String, String> = linkedMapOf(
            Pair("suicide-spam", "<ec>Please do not spam suicide. Continue will lead to a kick."),
            Pair("generic-spam", "<ec>Please do not spam commands. Continue will lead to a kick."),
        ),
        val kickMessage: Map<String, String> = linkedMapOf(
            Pair("suicide-spam", "<ec>Please do not spam suicide."),
            Pair("generic-spam", "<ec>Please do not spam commands."),
        ),
    ): ConfigurationPart

}